[project]
name = "lin-trans"
version = "0.2.1"
description = ""
authors = [
    {name = "riftcover", email = "<EMAIL>"}
]
readme = "README.md"
requires-python = ">=3.11,<3.13"
dependencies = [
    "httpx==0.27.2",
    "loguru==0.7.3",
    "modelscope==1.25.0",
    "openai==1.76.0",
    "sqlalchemy==2.0.40",
    "socksio==1.0.0",
    "packaging==25.0",
    "darkdetect==0.8.0",
    "colorthief==0.2.1",
    "path>=17.0.0",
    "scipy==1.15.2",
    "nuitka>=2.4.8",
    "numpy==1.24.4",
    "pysidesix-frameless-window==0.4.3",
    "torch==2.4.1",
    "torchaudio==2.4.1",
    "funasr==1.2.6",
    "pydantic>=2.9.2",
    "pyside6==6.7.2", # 必须这个版本，使用6.8.0 do_work() 线程工作完成后会卡住，无法进行下一步. mac上：6.7.2版本设置页ui显示错误， 6.9.1版本ui正确功能未验证
    "pyinstaller>=6.11.0",
    "av==14.4.0",
    "alibabacloud-oss-v2==1.1.0",
    "dashscope==1.23.1",
    "pydantic_settings==2.9.1",
    "dotenv==0.9.9",
    "pytz>=2025.2",
    "pip>=25.1.1",
]

[build-system]
requires = ["hatchling"]
build-backend = "hatchling.build"

[tool.hatch.build.targets.wheel]
packages = ["app"]

[tool.nuitka]
# 基本配置
main = "run.py"
output-filename = "LappedAI.exe"
output-dir = "dist"

# 编译模式
standalone = true
onefile = false

# 仅编译项目文件，第三方依赖不编译
nofollow-import-to = [
    "numpy",
    "scipy",
    "torch",
    "torchaudio",
    "PySide6",
    "funasr",
    "modelscope",
    "httpx",
    "loguru",
    "openai",
    "sqlalchemy",
    "socksio",
    "packaging",
    "darkdetect",
    "colorthief",
    "path",
    "av",
    "alibabacloud-oss-v2",
    "dashscope",
    "pydantic",
    "pydantic_settings",
    "dotenv",
    "pytz",
    "pip"
]

# 包含项目模块进行编译
follow-import-to = [
    "nice_ui",
    "app",
    "agent",
    "components",
    "orm",
    "services",
    "tools",
    "utils",
    "vendor",
    "videotrans"
]

# 包含数据文件
include-data-dir = [
    "components/assets=components/assets",
    "components/themes=components/themes",
    "nice_ui/language=nice_ui/language",
    "config=config"
]

# 包含数据文件
include-data-files = [
    "videotrans/set.ini=videotrans/set.ini",
    "videotrans/azure.txt=videotrans/azure.txt",
    "videotrans/chatgpt.txt=videotrans/chatgpt.txt",
    "videotrans/gemini.txt=videotrans/gemini.txt",
    "videotrans/localllm.txt=videotrans/localllm.txt",
    "videotrans/zijie.txt=videotrans/zijie.txt"
]

# Windows 特定配置
windows-console-mode = "disable"
windows-icon-from-ico = "components/assets/lapped.ico"

# 优化选项
prefer-source-code = false
show-progress = true
show-memory = false

# 插件配置
enable-plugin = [
    "pyside6",
    "numpy",
    "torch"
]

# 禁用一些不需要的功能
no-deployment-flag = "self-execution"
