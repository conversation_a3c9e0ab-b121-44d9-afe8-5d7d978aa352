@echo off
chcp 65001 >nul
echo 🚀 使用 Nuitka 构建 LappedAI 项目
echo ================================

REM 检查 Python 是否可用
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ 错误: 找不到 Python，请确保 Python 已安装并添加到 PATH
    pause
    exit /b 1
)

REM 检查 Nuitka 是否已安装
python -c "import nuitka" >nul 2>&1
if errorlevel 1 (
    echo ⚠️  警告: 未找到 Nuitka，正在安装...
    pip install nuitka
    if errorlevel 1 (
        echo ❌ 错误: Nuitka 安装失败
        pause
        exit /b 1
    )
)

REM 运行构建脚本
python build_nuitka.py

echo.
echo 按任意键退出...
pause >nul
