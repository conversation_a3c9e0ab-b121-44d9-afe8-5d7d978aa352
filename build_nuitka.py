#!/usr/bin/env python3
"""
Nuitka 构建脚本
使用 Nuitka 编译项目，仅编译项目文件，第三方依赖不进行编译
"""

import os
import sys
import shutil
import subprocess
from pathlib import Path


def clean_build_dir():
    """清理构建目录"""
    build_dirs = ["dist", "build", "*.build", "*.dist"]
    for pattern in build_dirs:
        if "*" in pattern:
            # 处理通配符模式
            for path in Path(".").glob(pattern):
                if path.is_dir():
                    print(f"删除构建目录: {path}")
                    shutil.rmtree(path, ignore_errors=True)
        else:
            path = Path(pattern)
            if path.exists():
                print(f"删除构建目录: {path}")
                if path.is_dir():
                    shutil.rmtree(path, ignore_errors=True)
                else:
                    path.unlink()


def build_with_nuitka():
    """使用 Nuitka 构建项目"""
    
    # 构建命令
    cmd = [
        sys.executable, "-m", "nuitka",
        
        # 基本配置
        "--main=run.py",
        "--output-filename=LappedAI.exe",
        "--output-dir=dist",
        
        # 编译模式
        "--standalone",
        "--assume-yes-for-downloads",
        
        # 仅编译项目文件，第三方依赖不编译
        "--nofollow-import-to=numpy",
        "--nofollow-import-to=scipy", 
        "--nofollow-import-to=torch",
        "--nofollow-import-to=torchaudio",
        "--nofollow-import-to=PySide6",
        "--nofollow-import-to=funasr",
        "--nofollow-import-to=modelscope",
        "--nofollow-import-to=httpx",
        "--nofollow-import-to=loguru",
        "--nofollow-import-to=openai",
        "--nofollow-import-to=sqlalchemy",
        "--nofollow-import-to=socksio",
        "--nofollow-import-to=packaging",
        "--nofollow-import-to=darkdetect",
        "--nofollow-import-to=colorthief",
        "--nofollow-import-to=path",
        "--nofollow-import-to=av",
        "--nofollow-import-to=alibabacloud-oss-v2",
        "--nofollow-import-to=dashscope",
        "--nofollow-import-to=pydantic",
        "--nofollow-import-to=pydantic_settings",
        "--nofollow-import-to=dotenv",
        "--nofollow-import-to=pytz",
        "--nofollow-import-to=pip",
        
        # 包含项目模块进行编译
        "--follow-import-to=nice_ui",
        "--follow-import-to=app",
        "--follow-import-to=agent", 
        "--follow-import-to=components",
        "--follow-import-to=orm",
        "--follow-import-to=services",
        "--follow-import-to=tools",
        "--follow-import-to=utils",
        "--follow-import-to=vendor",
        "--follow-import-to=videotrans",
        
        # 包含数据文件和目录
        "--include-data-dir=components/assets=components/assets",
        "--include-data-dir=components/themes=components/themes",
        "--include-data-dir=nice_ui/language=nice_ui/language", 
        "--include-data-dir=config=config",
        
        # 包含单个数据文件
        "--include-data-files=videotrans/set.ini=videotrans/set.ini",
        "--include-data-files=videotrans/azure.txt=videotrans/azure.txt",
        "--include-data-files=videotrans/chatgpt.txt=videotrans/chatgpt.txt",
        "--include-data-files=videotrans/gemini.txt=videotrans/gemini.txt",
        "--include-data-files=videotrans/localllm.txt=videotrans/localllm.txt",
        "--include-data-files=videotrans/zijie.txt=videotrans/zijie.txt",
        
        # Windows 特定配置
        "--windows-console-mode=disable",
        "--windows-icon-from-ico=components/assets/lapped.ico",
        
        # 优化选项
        "--show-progress",
        "--show-memory",
        
        # 插件配置
        "--enable-plugin=pyside6",
        "--enable-plugin=numpy",
        "--enable-plugin=torch",
        
        # 禁用一些不需要的功能
        "--no-deployment-flag=self-execution",
        
        # 输出详细信息
        "--verbose"
    ]
    
    print("开始使用 Nuitka 构建项目...")
    print(f"构建命令: {' '.join(cmd)}")
    
    try:
        # 执行构建命令
        result = subprocess.run(cmd, check=True, text=True)
        print("✅ 构建成功完成!")
        
        # 显示输出文件信息
        dist_dir = Path("dist")
        if dist_dir.exists():
            print(f"\n📁 构建输出目录: {dist_dir.absolute()}")
            for item in dist_dir.iterdir():
                if item.is_file():
                    size = item.stat().st_size / (1024 * 1024)  # MB
                    print(f"   📄 {item.name} ({size:.1f} MB)")
                elif item.is_dir():
                    print(f"   📁 {item.name}/")
        
        return True
        
    except subprocess.CalledProcessError as e:
        print(f"❌ 构建失败: {e}")
        return False
    except FileNotFoundError:
        print("❌ 错误: 找不到 Nuitka。请确保已安装 Nuitka:")
        print("   pip install nuitka")
        return False


def main():
    """主函数"""
    print("🚀 Nuitka 项目构建工具")
    print("=" * 50)
    
    # 检查是否在项目根目录
    if not Path("run.py").exists():
        print("❌ 错误: 请在项目根目录运行此脚本")
        sys.exit(1)
    
    # 清理构建目录
    print("🧹 清理旧的构建文件...")
    clean_build_dir()
    
    # 开始构建
    success = build_with_nuitka()
    
    if success:
        print("\n🎉 构建完成!")
        print("可执行文件位于: dist/LappedAI.exe")
    else:
        print("\n💥 构建失败!")
        sys.exit(1)


if __name__ == "__main__":
    main()
